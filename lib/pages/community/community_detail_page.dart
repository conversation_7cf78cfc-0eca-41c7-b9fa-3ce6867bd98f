import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../components/video_player_card.dart';
import '../../components/cache_image.dart';
import '../../components/vote_widget.dart';
import '../../model/emoji.dart';
import '../../model/forum_post_list.dart';
import '../../model/posts_list.dart';
import '../../model/vote_response.dart';
import '../../net/api/forum_service.dart';
import '../../net/api/posts_service.dart';
import '../../net/config/http_base_config.dart';
import '../../utils/log_util.dart';
import '../../utils/rich_text_parser.dart';
import '../../common/dl_color.dart';
import '../../info/forum_info.dart';
import 'user_profile_page.dart';
import 'more_actions_bottom_dialog.dart';
import 'report_dialog.dart';
import 'topic_page.dart';


class CommunityDetailPage extends StatefulWidget {
  final ForumPost post;

  const CommunityDetailPage({
    super.key,
    required this.post,
  });

  @override
  State<CommunityDetailPage> createState() => _CommunityDetailPageState();
}

class _CommunityDetailPageState extends State<CommunityDetailPage> {
  bool _isLoading = true;
  String? _error;
  ForumPost? _detailPost;
  final ForumService _forumService = ForumService();
  final PostsService _postsService = PostsService();
  bool _hasVoted = false; // 标记是否发生了投票
  
  // 评论相关
  bool _isLoadingComments = true;
  String? _commentsError;
  PostsList? _commentsList;
  String _sortType = 'hot'; // hot, latest
  
  // 视频播放器管理
  final Map<String, VideoPlayerController> _videoControllers = {};
  final Map<String, bool> _videoInitialized = {};
  final Map<String, bool> _videoBuffering = {};
  final Map<String, bool> _videoLoading = {};

  @override
  void initState() {
    super.initState();
    _loadPostDetail();
    // _loadComments(); // 暂时注释掉评论加载
  }

  @override
  void dispose() {
    // 销毁所有视频控制器
    for (final controller in _videoControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadPostDetail() async {
    if (!mounted) return;
    
    LogUtil.d('开始加载帖子详情', tag: 'PostDetail');
    
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await _forumService.getPostDetail(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        threadId: widget.post.threadId,
        context: context
      );

      if (!mounted) return;

      LogUtil.d('接口响应: code=${response.code}, message=${response.message}', tag: 'PostDetail');

      if (response.code == 0 && response.data != null) {
        setState(() {
          _detailPost = response.data;
          _isLoading = false;
        });
        LogUtil.d('帖子详情加载成功', tag: 'PostDetail');
      } else {
        setState(() {
          _error = response.message ?? '获取帖子详情失败';
          _isLoading = false;
        });
        LogUtil.w('帖子详情加载失败: ${response.message}', tag: 'PostDetail');
      }
    } catch (e, stackTrace) {
      if (!mounted) return;
      setState(() {
        _error = '网络错误: $e';
        _isLoading = false;
      });
      LogUtil.e('帖子详情加载异常', error: e, stackTrace: stackTrace, tag: 'PostDetail');
    }
  }

  /*
  Future<void> _loadComments() async {
    if (!mounted) return;
    
    LogUtil.d('开始加载评论列表', tag: 'Comments');
    
    setState(() {
      _isLoadingComments = true;
      _commentsError = null;
    });

    try {
      final response = await _postsService.getPostsList(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        threadId: widget.post.threadId,
        page: 1,
        pageSize: 20,
        sort: _getApiSortValue(_sortType),
      );

      if (!mounted) return;

      LogUtil.d('评论接口响应: code=${response.code}, message=${response.message}', tag: 'Comments');

      if (response.code == 0 && response.data != null) {
        setState(() {
          _commentsList = response.data;
          _isLoadingComments = false;
        });
        LogUtil.d('评论列表加载成功', tag: 'Comments');
      } else {
        setState(() {
          _commentsError = response.message ?? '获取评论失败';
          _isLoadingComments = false;
        });
        LogUtil.w('评论列表加载失败: ${response.message}', tag: 'Comments');
      }
    } catch (e, stackTrace) {
      if (!mounted) return;
      setState(() {
        _commentsError = '网络错误: $e';
        _isLoadingComments = false;
      });
      LogUtil.e('评论列表加载异常', error: e, stackTrace: stackTrace, tag: 'Comments');
    }
  }
  */

  /*
  void _changeSortType(String sortType) {
    if (_sortType != sortType) {
      setState(() {
        _sortType = sortType;
      });
      _loadComments();
    }
  }
  */

  /*
  String _getApiSortValue(String sortType) {
    switch (sortType) {
      case 'hot':
        return 'hot';
      case 'latest':
        return '-createdAt';
      default:
        return 'hot';
    }
  }
  */
  @override
  Widget build(BuildContext context) {
    // 显示加载状态
    if (_isLoading) {
      return Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              // 主要内容区域
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 简单的返回按钮区域
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            IconButton(
                              icon: const Icon(Icons.arrow_back),
                              onPressed: () => Navigator.pop(context, _hasVoted),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                            const SizedBox(width: 8),
                            const Text(
                              '帖子详情',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 200),
                      const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 显示错误状态
    if (_error != null) {
      return Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              // 主要内容区域
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 简单的返回按钮区域
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            IconButton(
                              icon: const Icon(Icons.arrow_back),
                              onPressed: () => Navigator.pop(context, _hasVoted),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                            const SizedBox(width: 8),
                            const Text(
                              '帖子详情',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 100),
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _error!,
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadPostDetail,
                              child: const Text('重试'),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 使用详情数据，如果没有详情数据则使用传入的数据
    final post = _detailPost ?? widget.post;

    return WillPopScope(
      onWillPop: () async {
        Navigator.pop(context, _hasVoted);
        return false;
      },
      child: Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              // 主要内容区域
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 用户信息区域
                      _buildUserHeader(post),

                      // 帖子标题和内容（白色背景容器）
                      _buildPostTitleAndContent(post),

                      // 评论区域 - 暂时注释掉
                      // _buildCommentsSection(),

                      const SizedBox(height: 20), // 为底部留出空间
                    ],
                  ),
                ),
              ),
              
              // 底部评论输入栏
              // _buildBottomCommentBar(post),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomCommentBar(ForumPost post) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey[200]!,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 输入框
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // 不需要做任何操作
                },
                child: Container(
                  height: 40,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    children: [
                      Text(
                        '写点什么吧',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(width: 16),
            
            // 收藏按钮
            GestureDetector(
              onTap: () {
                // TODO: 处理收藏逻辑
                _toggleFavorite(post);
              },
              child: Row(
                children: [
                  Icon(
                    Icons.star_outline,
                    size: 24,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    post.favorCount.toString(),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 20),
            
            // 点赞按钮
            GestureDetector(
              onTap: () {
                // TODO: 处理点赞逻辑
                _toggleLike(post);
              },
              child: Row(
                children: [
                  Icon(
                    Icons.thumb_up_outlined,
                    size: 24,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatCount(post.likeReward.likePayCount),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCommentInput() {
    // TODO: 实现评论输入功能
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('发表评论'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: '写点什么吧...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              // TODO: 提交评论
              Navigator.pop(context);
            },
            child: const Text('发布'),
          ),
        ],
      ),
    );
  }

  void _toggleFavorite(ForumPost post) {
    // TODO: 实现收藏切换逻辑
    setState(() {
      // 这里可以更新收藏状态
    });
  }

  void _toggleLike(ForumPost post) {
    // TODO: 实现点赞切换逻辑
    setState(() {
      // 这里可以更新点赞状态
    });
  }

  Widget _buildUserHeader(ForumPost post) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // 返回按钮
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context, _hasVoted),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: () => _navigateToUserProfile(post.user),
            child: CircleAvatar(
              radius: 24,
              backgroundColor: Colors.grey[200],
              backgroundImage: post.user.avatar.isNotEmpty 
                  ? NetworkImage(post.user.avatar) 
                  : null,
              child: post.user.avatar.isEmpty 
                  ? Icon(
                      Icons.person,
                      size: 24,
                      color: Colors.grey[600],
                    )
                  : null,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      post.user.nickname.isNotEmpty ? post.user.nickname : '匿名用户',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (post.user.badge.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.blue[200]!, width: 0.5),
                        ),
                        child: Text(
                          '官方账号',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.blue[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  '发帖 ${post.user.threadCount} · 粉丝 ${post.user.fansCount}',
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          // 使用更多设置图标，点击弹出更多操作
          GestureDetector(
            onTap: () async {
              await MoreActionsBottomDialog.show(
                context: context,
                onReport: () async {
                  await showReportDialog(
                    context,
                    threadId: widget.post.threadId,
                    userId: widget.post.user.userId,
                  );
                },
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              child: Image.asset(
                'assets/images/more_set_icon.png',
                width: 20,
                height: 20,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostTitleAndContent(ForumPost post) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 0.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 帖子标题
          if (post.title.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: _buildTextWithEmojis(
                post.title,
                textStyle: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  height: 1.3,
                ),
              ),
            ),
          
          // 帖子内容
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            child: _buildRichTextContent(post.content.text, post.content.images),
          ),
          
          // 根据是否有投票模块决定话题标签的位置
          if (post.voteId == null) _buildTopicTags(post),

          // 投票模块（根据vote_id判断是否显示）
          if (post.voteId != null) 
            VoteWidget(
              post: post,
              displayType: VoteDisplayType.detail,
              onVoteSuccess: (updatedPost) {
                // 投票成功后刷新帖子详情
                _loadPostDetail();
                // 标记发生了投票
                _hasVoted = true;
              },
            ),

          // 如果有投票模块，话题标签显示在投票模块之后，并添加间距
          if (post.voteId != null) ...[
            const SizedBox(height: 12),
            _buildTopicTags(post),
          ],
          
          // 时间戳和统计信息
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Row(
              children: [
                Text(
                  _formatTimeAgo(post.createdAt),
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
                if (post.location.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  Text(
                    '来自${post.location}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
                const Spacer(),
                Row(
                  children: [
                    Icon(
                      Icons.visibility_outlined,
                      size: 16,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatCount(post.viewCount),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建话题标签
  Widget _buildTopicTags(ForumPost post) {
    // 如果没有话题标签，返回空Widget
    if (post.topics.trim().isEmpty) {
      return const SizedBox.shrink();
    }

    List<Map<String, String>> topicList = _parseTopics(post.topics);

    if (topicList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: topicList.map((topicData) {
              return GestureDetector(
                onTap: () => _navigateToTopicPage(topicData['id'] ?? topicData['content'] ?? ''),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '#${topicData['content'] ?? topicData['id'] ?? ''}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  /// 解析话题标签字符串
  List<Map<String, String>> _parseTopics(String topicsStr) {
    List<Map<String, String>> result = [];

    try {
      // 尝试解析为JSON
      dynamic parsed = json.decode(topicsStr);

      if (parsed is List) {
        // JSON数组格式: [{"id": "1", "content": "游戏"}, {"id": "2", "content": "攻略"}]
        for (var item in parsed) {
          if (item is Map<String, dynamic>) {
            result.add({
              'id': item['id']?.toString() ?? '',
              'content': item['content']?.toString() ?? '',
            });
          }
        }
      } else if (parsed is Map<String, dynamic>) {
        // 单个JSON对象格式: {"id": "1", "content": "游戏"}
        result.add({
          'id': parsed['id']?.toString() ?? '',
          'content': parsed['content']?.toString() ?? '',
        });
      }
    } catch (e) {
      // JSON解析失败，尝试作为简单字符串处理
      List<String> simpleTopics = topicsStr
          .split(RegExp(r'[,，\s]+'))
          .where((topic) => topic.trim().isNotEmpty)
          .map((topic) => topic.trim())
          .toList();

      for (String topic in simpleTopics) {
        result.add({
          'id': topic,
          'content': topic,
        });
      }
    }

    return result;
  }

  /// 跳转到话题页面
  void _navigateToTopicPage(String topicIdStr) {
    final int? topicId = int.tryParse(topicIdStr);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TopicPage(
          topicName: topicIdStr,
          topicId: topicId,
          description: null,
          avatarUrl: null,
          viewCount: 0,
          likeCount: 0,
          threadCount: 0,
        ),
      ),
    );
  }

  Widget _buildPostTitle(ForumPost post) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Text(
        post.title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
          height: 1.3,
        ),
      ),
    );
  }

  Widget _buildPostMeta(ForumPost post) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          Row(
            children: [
              Text(
                _formatTimeAgo(post.createdAt),
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
              const Spacer(),
              Row(
                children: [
                  Icon(
                    Icons.visibility_outlined,
                    size: 16,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatCount(post.viewCount),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.favorite_outline,
                    size: 16,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatCount(post.likeReward.likePayCount),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRichTextContent(String htmlContent, List<dynamic> images) {
    final post = _detailPost ?? widget.post;
    
    // 如果 htmlContent 为空，但 pureText 有内容，使用 pureText
    String contentToUse = htmlContent;
    if (htmlContent.trim().isEmpty && post.content.pureText.trim().isNotEmpty) {
      contentToUse = post.content.pureText;
    }
    
    // 判断帖子类型
    final bool isMixedContent = post.from == 2 || post.isMixThread == 1;
    
    if (isMixedContent && (contentToUse.contains('<img') || contentToUse.contains('<video'))) {
      // 图文混排模式：HTML中包含img和video标签
      return _buildMixedContent(contentToUse, post.content.images);
    } else if (isMixedContent || contentToUse.contains('<')) {
      // 混合内容或包含HTML标签，使用混合内容解析
      return _buildMixedContent(contentToUse, post.content.images);
    } else {
      // 分离模式：文本和媒体分开显示
      return _buildSeparatedContent(contentToUse, post);
    }
  }
  
  /// 构建图文混排内容
  Widget _buildMixedContent(String htmlContent, List<dynamic> contentImages) {
    final elements = RichTextParser.parseHtmlContent(htmlContent);
    final List<Widget> widgets = [];
    int imageIndex = 0;
    
    for (final element in elements) {
      switch (element.type) {
        case ElementType.text:
          if (element.content.isNotEmpty || (element.mixedInlineElements != null && element.mixedInlineElements!.isNotEmpty)) {
            widgets.add(_buildTextElement(element));
          }
          break;
        case ElementType.image:
          // 从HTML中提取图片URL，而不是从contentImages数组
          String? imageUrl = RichTextParser.extractImageUrlFromHtml(htmlContent, imageIndex);
          if (imageUrl != null) {
            widgets.add(_buildImageWidget(imageUrl, contentImages));
          }
          imageIndex++;
          break;
        case ElementType.video:
          if (element.src != null) {
            widgets.add(_buildVideoWidget(element.src!, null));
          }
          break;
      }
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets.isEmpty ? [_buildEmptyPlaceholder()] : widgets,
    );
  }

  /// 构建分离内容
  Widget _buildSeparatedContent(String htmlContent, ForumPost post) {
    final List<Widget> widgets = [];
    
    // 提取并显示文本
    final textContent = RichTextParser.extractPlainText(htmlContent);
    if (textContent.isNotEmpty) {
      final paragraphs = textContent.split('\n').where((p) => p.trim().isNotEmpty);
      for (final paragraph in paragraphs) {
        widgets.add(_buildPlainTextWidget(paragraph.trim()));
      }
    }
    
    // 显示content.images中的图片
    final contentImages = post.content.images;
    for (final imageData in contentImages) {
      final imageUrl = RichTextParser.extractImageUrl(imageData);
      if (imageUrl != null) {
        widgets.add(_buildImageWidget(imageUrl, contentImages));
      }
    }
    
    // 显示indexes中的图片和视频
    widgets.addAll(_buildSeparatedImages(post));
    widgets.addAll(_buildSeparatedVideos(post));
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets.isEmpty ? [_buildEmptyPlaceholder()] : widgets,
    );
  }
  
  /// 从HTML中提取第N个图片的URL
  String? _extractImageUrlFromHtml(String html, int imageIndex) {
    return RichTextParser.extractImageUrlFromHtml(html, imageIndex);
  }

  /// 提取图片URL
  String? _extractImageUrl(dynamic imageData) {
    return RichTextParser.extractImageUrl(imageData);
  }

  /// 构建带表情包的文本Widget
  Widget _buildTextWithEmojis(String text, {TextStyle? textStyle}) {
    final emojis = ForumInfo().emojis;
    if (emojis.isEmpty || !text.contains(':')) {
      return Text(text, style: textStyle);
    }

    final List<InlineSpan> spans = [];
    final regex = RegExp(r':([^:]+):');
    int currentIndex = 0;
    
    for (final match in regex.allMatches(text)) {
      // 添加表情前的文本
      if (match.start > currentIndex) {
        spans.add(TextSpan(
          text: text.substring(currentIndex, match.start),
          style: textStyle,
        ));
      }
      
      // 查找表情包 - 直接遍历emojis列表查找匹配的code
      final emojiCode = match.group(1);
      final fullEmojiCode = ":${emojiCode}:";
      Emoji? foundEmoji;
      
      for (final emoji in emojis) {
        if (emoji.code == fullEmojiCode) {
          foundEmoji = emoji;
          break;
        }
      }
      
      if (foundEmoji != null && foundEmoji.url.isNotEmpty) {
        // 添加表情包图片
        spans.add(WidgetSpan(
          alignment: PlaceholderAlignment.middle,
          child: Container(
            width: 20,
            height: 20,
            child: CachedNetworkImage(
              imageUrl: foundEmoji.url,
              width: 20,
              height: 20,
              fit: BoxFit.contain,
              placeholder: (context, url) => Text(':$emojiCode:', style: textStyle),
              errorWidget: (context, url, error) {
                print('表情包加载失败: $url, 错误: $error');
                return Text(':$emojiCode:', style: textStyle);
              },
            ),
          ),
        ));
      } else {
        // 如果找不到表情包，保持原文本
        spans.add(TextSpan(
          text: ':$emojiCode:',
          style: textStyle,
        ));
      }
      
      currentIndex = match.end;
    }
    
    // 添加剩余文本
    if (currentIndex < text.length) {
      spans.add(TextSpan(
        text: text.substring(currentIndex),
        style: textStyle,
      ));
    }
    
    return Text.rich(TextSpan(children: spans));
  }
  /// 构建文本元素
  Widget _buildTextElement(ContentElement element) {
    // 检查是否有混合内联元素（包含文本和图片）
    if (element.mixedInlineElements != null && element.mixedInlineElements!.isNotEmpty) {
      return _buildMixedInlineContent(element);
    }
    
    // 使用 RichTextParser 的方法来构建多样式文本或单一样式文本
    if (element.styledTexts != null && element.styledTexts!.length > 1) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: RichTextParser.buildRichTextWidget(element),
      );
    }
    
    // 确保文本颜色在有背景色时可见
    Color textColor = element.textStyle?.color ?? Colors.black87;
    if (element.backgroundColor != null && textColor == element.backgroundColor) {
      // 当有背景色时，如果没有明确设置文本颜色，使用黑色确保可见性
      textColor = Colors.black;
    }
    
    final textStyle = (element.textStyle ?? const TextStyle(
      fontSize: 16,
      height: 1.6,
    )).copyWith(
      color: textColor,
      decorationColor: textColor, // 下划线颜色与字体颜色一致
    );
    
    Widget textWidget = _buildTextWithEmojis(element.content, textStyle: textStyle);
    
    // 添加背景色
    if (element.backgroundColor != null) {
      textWidget = IntrinsicWidth(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            color: element.backgroundColor,
            borderRadius: BorderRadius.circular(4),
          ),
          child: textWidget,
        ),
      );
    }
    
    // 根据textAlign决定对齐方式
    Widget alignedWidget;
    if (element.textAlign != null) {
      alignedWidget = Align(
        alignment: _getAlignmentFromTextAlign(element.textAlign),
        child: textWidget,
      );
    } else {
      alignedWidget = textWidget;
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: alignedWidget,
    );
  }
  
  /// 构建混合内联内容（文本+图片）
  Widget _buildMixedInlineContent(ContentElement element) {
    if (element.mixedInlineElements == null || element.mixedInlineElements!.isEmpty) {
      return const SizedBox.shrink();
    }
    
    List<InlineSpan> inlineSpans = [];
    
    for (var inlineElement in element.mixedInlineElements!) {
      if (inlineElement is StyledText) {
        // 文本元素
        inlineSpans.add(TextSpan(
          text: inlineElement.text,
          style: inlineElement.textStyle,
        ));
      } else if (inlineElement is Map<String, dynamic> && inlineElement['type'] == 'image') {
        // 图片元素 - 使用 WidgetSpan 来内联显示图片
        String imageUrl = inlineElement['url'];
        inlineSpans.add(WidgetSpan(
          alignment: PlaceholderAlignment.middle,
          child: GestureDetector(
            onTap: () => _showFullScreenImage(imageUrl),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              constraints: const BoxConstraints(
                maxWidth: 200, // 限制内联图片的最大宽度
                maxHeight: 100, // 限制内联图片的最大高度
              ),
              child: CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.contain,
                placeholder: (context, url) => Container(
                  width: 50,
                  height: 30,
                  color: Colors.grey[200],
                  child: const Center(
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  width: 50,
                  height: 30,
                  color: Colors.grey[200],
                  child: const Icon(Icons.broken_image, size: 20),
                ),
              ),
            ),
          ),
        ));
      }
    }
    
    if (inlineSpans.isEmpty) {
      return const SizedBox.shrink();
    }
    
    Widget contentWidget = RichText(
      textAlign: element.textAlign ?? TextAlign.left,
      text: TextSpan(
        children: inlineSpans,
        style: const TextStyle(
          fontSize: 16,
          height: 1.6,
          color: Colors.black87,
        ),
      ),
    );
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: contentWidget,
    );
  }

  /// 将TextAlign转换为Alignment
  Alignment _getAlignmentFromTextAlign(TextAlign? textAlign) {
    switch (textAlign) {
      case TextAlign.center: return Alignment.center;
      case TextAlign.left: return Alignment.centerLeft;
      case TextAlign.right: return Alignment.centerRight;
      default: return Alignment.centerLeft;
    }
  }

  /// 构建纯文本组件
  Widget _buildPlainTextWidget(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: _buildTextWithEmojis(
        text,
        textStyle: const TextStyle(
          fontSize: 16,
          height: 1.6,
          color: Colors.black87,
        ),
      ),
    );
  }

  /// 构建空内容占位符
  Widget _buildEmptyPlaceholder() {
    return Container(
      height: 100,
      width: double.infinity,
      color: Colors.grey[100],
      child: const Center(
        child: Text('暂无内容', style: TextStyle(color: Colors.grey)),
      ),
    );
  }
  
  
  /// 处理图文分离的图片
  List<Widget> _buildSeparatedImages(ForumPost post) {
    final List<Widget> widgets = [];
    final indexes = post.content.indexes;
    
    // 从indexes[101]获取图片
    if (indexes.containsKey('101')) {
      final imageIndex = indexes['101'];
      if (imageIndex is Map<String, dynamic> && imageIndex['body'] is List) {
        final bodyList = imageIndex['body'] as List;
        
        for (var item in bodyList) {
          if (item is Map<String, dynamic>) {
            final imageUrl = item['url'] ?? item['thumbUrl'] ?? item['attachment'];
            if (imageUrl != null && imageUrl.isNotEmpty) {
              widgets.add(_buildImageWidget(imageUrl, bodyList));
            }
          }
        }
      }
    }
    
    return widgets;
  }
  
  /// 处理图文分离的视频
  List<Widget> _buildSeparatedVideos(ForumPost post) {
    final List<Widget> widgets = [];
    final indexes = post.content.indexes;
    
    // 从indexes[108]获取视频
    if (indexes.containsKey('108')) {
      final videoIndex = indexes['108'];
      if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
        final bodyList = videoIndex['body'] as List;
        
        for (var item in bodyList) {
          if (item is Map<String, dynamic>) {
            final videoUrl = item['url'];
            String? coverUrl;
            
            // 安全地获取封面URL
            final cover = item['cover'];
            if (cover is Map<String, dynamic>) {
              coverUrl = cover['url'];
            } else if (cover is List && cover.isNotEmpty) {
              final firstItem = cover.first;
              if (firstItem is Map<String, dynamic>) {
                coverUrl = firstItem['url'];
              }
            }
            
            if (videoUrl != null && videoUrl.isNotEmpty) {
              widgets.add(_buildVideoWidget(videoUrl, coverUrl));
            }
          }
        }
      }
    }
    
    return widgets;
  }
  
  /// 构建视频widget - 支持内联播放和全屏播放
  Widget _buildVideoWidget(String videoUrl, String? coverUrl) {
    return VideoPlayerCard(videoUrl: videoUrl, coverUrl: coverUrl, autoPlay: false, height: 200);
  }


  String _extractTextFromHtml(String html) {
    if (html.isEmpty) return '';
    
    String result = html;
    
    // 1. 处理块级元素 - 转换为段落分隔
    result = result
        .replaceAll(RegExp(r'<div[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</div>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<p[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</p>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<h[1-6][^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</h[1-6]>', caseSensitive: false), '\n');
    
    // 2. 处理换行标签
    result = result
        .replaceAll(RegExp(r'<br[^>]*/?>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<hr[^>]*/?>', caseSensitive: false), '\n——————————\n');
    
    // 3. 处理列表元素
    result = result
        .replaceAll(RegExp(r'<ul[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</ul>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<ol[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</ol>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<li[^>]*>', caseSensitive: false), '\n• ')
        .replaceAll(RegExp(r'</li>', caseSensitive: false), '');
    
    // 4. 处理表格元素
    result = result
        .replaceAll(RegExp(r'<table[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</table>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<tr[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</tr>', caseSensitive: false), '')
        .replaceAll(RegExp(r'<td[^>]*>', caseSensitive: false), ' ')
        .replaceAll(RegExp(r'</td>', caseSensitive: false), ' | ')
        .replaceAll(RegExp(r'<th[^>]*>', caseSensitive: false), ' ')
        .replaceAll(RegExp(r'</th>', caseSensitive: false), ' | ');
    
    // 5. 处理强调和样式标签 - 保留语义
    result = result
        .replaceAll(RegExp(r'<strong[^>]*>', caseSensitive: false), '**')
        .replaceAll(RegExp(r'</strong>', caseSensitive: false), '**')
        .replaceAll(RegExp(r'<b[^>]*>', caseSensitive: false), '**')
        .replaceAll(RegExp(r'</b>', caseSensitive: false), '**')
        .replaceAll(RegExp(r'<em[^>]*>', caseSensitive: false), '*')
        .replaceAll(RegExp(r'</em>', caseSensitive: false), '*')
        .replaceAll(RegExp(r'<i[^>]*>', caseSensitive: false), '*')
        .replaceAll(RegExp(r'</i>', caseSensitive: false), '*')
        .replaceAll(RegExp(r'<u[^>]*>', caseSensitive: false), '_')
        .replaceAll(RegExp(r'</u>', caseSensitive: false), '_');
    
    // 6. 处理链接标签 - 提取链接文本
    result = result.replaceAllMapped(
      RegExp(r'<a[^>]*href=["\x27]([^"\x27]*)["\x27][^>]*>(.*?)</a>', caseSensitive: false),
      (match) {
        String linkText = match.group(2) ?? '';
        String url = match.group(1) ?? '';
        if (linkText.isNotEmpty && url.isNotEmpty && linkText != url) {
          return '$linkText ($url)';
        } else if (linkText.isNotEmpty) {
          return linkText;
        } else {
          return url;
        }
      },
    );
    
    // 7. 处理引用块
    result = result
        .replaceAll(RegExp(r'<blockquote[^>]*>', caseSensitive: false), '\n> ')
        .replaceAll(RegExp(r'</blockquote>', caseSensitive: false), '\n');
    
    // 8. 处理代码块
    result = result
        .replaceAll(RegExp(r'<pre[^>]*>', caseSensitive: false), '\n```\n')
        .replaceAll(RegExp(r'</pre>', caseSensitive: false), '\n```\n')
        .replaceAll(RegExp(r'<code[^>]*>', caseSensitive: false), '`')
        .replaceAll(RegExp(r'</code>', caseSensitive: false), '`');
    
    // 9. 移除所有剩余的HTML标签
    result = result.replaceAll(RegExp(r'<[^>]*>'), '');
    
    // 10. 替换HTML实体
    result = result
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&#34;', '"')
        .replaceAll('&ldquo;', '"')
        .replaceAll('&rdquo;', '"')
        .replaceAll('&lsquo;', "'")
        .replaceAll('&rsquo;', "'")
        .replaceAll('&mdash;', '—')
        .replaceAll('&ndash;', '–')
        .replaceAll('&hellip;', '…')
        .replaceAll('&copy;', '©')
        .replaceAll('&reg;', '®')
        .replaceAll('&trade;', '™');
    
    // 11. 清理多余的空白字符和换行
    result = result
        // 替换多个连续空格为单个空格
        .replaceAll(RegExp(r' +'), ' ')
        // 替换多个连续换行为最多两个换行（段落分隔）
        .replaceAll(RegExp(r'\n\s*\n\s*\n+'), '\n\n')
        // 清理行首行尾空白
        .replaceAll(RegExp(r'\n +'), '\n')
        .replaceAll(RegExp(r' +\n'), '\n')
        // 移除开头和结尾的空白
        .trim();
    
    return result;
  }

  Widget _buildTextWidget(String text) {
    if (text.isEmpty) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          height: 1.6,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildImageWidget(String imageUrl, [List<dynamic>? allImages]) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: GestureDetector(
          onTap: () => _showFullScreenImage(imageUrl, allImages),
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            width: double.infinity,
            fit: BoxFit.fitWidth,
            placeholder: (context, url) => Container(
              height: 200,
              width: double.infinity,
              color: Colors.grey[100],
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 8),
                    Text('图片加载中...', style: TextStyle(fontSize: 12)),
                  ],
                ),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              height: 200,
              width: double.infinity,
              color: Colors.red[100],
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, color: Colors.red, size: 32),
                    SizedBox(height: 8),
                    Text('图片加载失败', style: TextStyle(fontSize: 12, color: Colors.red)),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showFullScreenImage(String imageUrl, [List<dynamic>? allImages]) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) {
        // 如果有多张图片，使用图片库，否则只显示单张图片
        if (allImages != null && allImages.length > 1) {
          int initialIndex = 0;
          for (int i = 0; i < allImages.length; i++) {
            final imageData = allImages[i];
            String? url;
            if (imageData is Map<String, dynamic>) {
              url = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
            } else if (imageData is String) {
              url = imageData;
            }
            if (url == imageUrl) {
              initialIndex = i;
              break;
            }
          }
          return _ImageGalleryDialog(images: allImages, initialIndex: initialIndex);
        } else {
          return GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Dialog.fullscreen(
              backgroundColor: Colors.transparent,
              child: Stack(
                children: [
                  Center(
                    child: InteractiveViewer(
                      panEnabled: true,
                      scaleEnabled: true,
                      minScale: 0.5,
                      maxScale: 3.0,
                      child: CachedImage(
                        imageUrl: imageUrl,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 50,
                    right: 20,
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: const BoxDecoration(
                          color: Colors.black54,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
      },
    );
  }

  String _formatTimeAgo(String createdAt) {
    try {
      final DateTime postTime = DateTime.parse(createdAt);
      final DateTime now = DateTime.now();
      final Duration diff = now.difference(postTime);
      
      if (diff.inMinutes < 60) {
        return '${diff.inMinutes}分钟前';
      } else if (diff.inHours < 24) {
        return '${diff.inHours}小时前';
      } else {
        // 超过1天直接显示日期时间
        return '${postTime.year}-${postTime.month.toString().padLeft(2, '0')}-${postTime.day.toString().padLeft(2, '0')} ${postTime.hour.toString().padLeft(2, '0')}:${postTime.minute.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      return '刚刚';
    }
  }

  String _formatCount(int count) {
    if (count > 10000) {
      return '${(count / 10000).toStringAsFixed(1)}w';
    } else if (count > 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }

  Widget _buildCommentsSection() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 评论标题和排序
          _buildCommentsHeader(),
          
          // 评论列表
          if (_isLoadingComments)
            const Padding(
              padding: EdgeInsets.all(40),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (_commentsError != null)
            Padding(
              padding: const EdgeInsets.all(20),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _commentsError!,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: () {}, // 注释掉评论重试功能
                      child: const Text('重试'),
                    ),
                  ],
                ),
              ),
            )
          else if (_commentsList?.pageData.isEmpty ?? true)
            Padding(
              padding: const EdgeInsets.all(40),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.message_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '暂无评论',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            _buildCommentsList(),
        ],
      ),
    );
  }

  Widget _buildCommentsHeader() {
    final commentCount = _commentsList?.totalCount ?? 75;
    
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 6, 16, 0),
      child: Column(
        children: [
          // 评论数量和排序按钮
          Row(
            children: [
              Text(
                '全部回复 (${commentCount}条)',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              // 排序选项
              Container(
                padding: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    _buildSortTab('最热', 'hot'),
                    _buildSortTab('最新', 'latest'),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          const Divider(height: 1),
        ],
      ),
    );
  }

  Widget _buildSortTab(String label, String sortType) {
    final isSelected = _sortType == sortType;
    
    return GestureDetector(
      onTap: () {}, // 注释掉排序功能
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(14),
          boxShadow: isSelected ? [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 0,
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ] : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 13,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            color: isSelected ? Colors.black87 : Colors.grey[600],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentsList() {
    final comments = _commentsList?.pageData ?? [];
    
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: comments.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        return _buildCommentItem(comments[index]);
      },
    );
  }

  Widget _buildCommentItem(Post comment) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户头像
          GestureDetector(
            onTap: () => _navigateToUserProfileFromComment(comment.user),
            child: CircleAvatar(
              radius: 20,
              backgroundColor: Colors.grey[200],
              backgroundImage: comment.user.avatar.isNotEmpty 
                  ? NetworkImage(comment.user.avatar) 
                  : null,
              child: comment.user.avatar.isEmpty 
                  ? Icon(
                      Icons.person,
                      size: 20,
                      color: Colors.grey[600],
                    )
                  : null,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 评论内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户名和时间
                Row(
                  children: [
                    Text(
                      comment.user.nickname.isNotEmpty ? comment.user.nickname : '匿名用户',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatTimeAgo(comment.createdAt),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 6),
                
                // 评论文本
                if (comment.content.isNotEmpty)
                  Text(
                    RichTextParser.extractTextFromHtml(comment.content),
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                
                // 显示图片
                if (comment.images.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: comment.images.map((image) {
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: GestureDetector(
                            onTap: () => _showFullScreenImage(image.url, comment.images.map((img) => {'url': img.url}).toList()),
                            child: CachedNetworkImage(
                              imageUrl: image.url,
                              width: 180,
                              height: 180,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                width: 180,
                                height: 180,
                                color: Colors.grey[200],
                                child: const Center(
                                  child: CircularProgressIndicator(),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                width: 180,
                                height: 180,
                                color: Colors.grey[200],
                                child: const Icon(Icons.error),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                
                const SizedBox(height: 8),
                
                // 二级评论
                if (comment.lastThreeComments.isNotEmpty)
                  _buildSecondaryComments(comment),
                
                // 互动按钮
                Row(
                  children: [
                    _buildCommentActionButton(
                      Icons.chat_bubble_outline,
                      '回复',
                      () {},
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecondaryComments(Post parentComment) {
    return Container(
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...parentComment.lastThreeComments.map((secondaryComment) => 
            _buildSecondaryCommentItem(secondaryComment)),
          
          // 如果有更多评论，显示"查看全部X条评论"
          if (parentComment.replyCount > parentComment.lastThreeComments.length)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: GestureDetector(
                onTap: () {
                  // TODO: 跳转到评论详情页或展开更多评论
                },
                child: Text(
                  '查看全部${parentComment.replyCount}条评论',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.blue[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSecondaryCommentItem(Post secondaryComment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户名和回复对象
          Row(
            children: [
              Text(
                secondaryComment.user.nickname.isNotEmpty ? secondaryComment.user.nickname : '匿名用户',
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue[600],
                ),
              ),
              if (secondaryComment.replyUser != null) ...[
                Text(
                  ' 回复 ',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  secondaryComment.replyUser!.nickname.isNotEmpty 
                      ? secondaryComment.replyUser!.nickname 
                      : '匿名用户',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: Colors.blue[600],
                  ),
                ),
              ],
              Text(
                ': ',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          // 评论内容
          if (secondaryComment.content.isNotEmpty)
            Text(
              RichTextParser.extractTextFromHtml(secondaryComment.content),
              style: const TextStyle(
                fontSize: 13,
                color: Colors.black87,
                height: 1.3,
              ),
            ),
          
          // 显示图片（如果有）
          if (secondaryComment.images.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 6),
              child: GestureDetector(
                onTap: () => _showFullScreenImage(
                  secondaryComment.images.first.url, 
                  secondaryComment.images.map((img) => {'url': img.url}).toList()
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.image,
                      size: 16,
                      color: Colors.blue[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '查看图片',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.blue[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          
          // 点赞（如果有）
          // 隐藏二级评论点赞展示
        ],
      ),
    );
  }

  Widget _buildCommentActionButton(
    IconData icon,
    String text,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey[500],
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// 跳转到用户个人资料页面
  void _navigateToUserProfile(ForumUser user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfilePage(user: user),
      ),
    );
  }

  /// 从评论跳转到用户个人资料页面
  void _navigateToUserProfileFromComment(PostUser commentUser) {
    // 将评论用户转换为ForumUser格式
    final forumUser = ForumUser(
      userId: commentUser.id,
      nickname: commentUser.nickname,
      avatar: commentUser.avatar,
      badge: commentUser.badge ?? '',
      label: commentUser.label ?? '',
      color: commentUser.color ?? '',
      threadCount: 0,
      followCount: 0,
      fansCount: 0,
      likedCount: 0,
      questionCount: 0,
      isRealName: commentUser.isReal,
      joinedAt: '',
      follow: 0,
    );
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfilePage(user: forumUser),
      ),
    );
  }
}

/// 图片画廊弹窗组件
class _ImageGalleryDialog extends StatefulWidget {
  final List<dynamic> images;
  final int initialIndex;

  const _ImageGalleryDialog({
    required this.images,
    required this.initialIndex,
  });

  @override
  State<_ImageGalleryDialog> createState() => _ImageGalleryDialogState();
}

class _ImageGalleryDialogState extends State<_ImageGalleryDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  String? _getImageUrl(dynamic imageData) {
    if (imageData is Map<String, dynamic>) {
      return imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      return imageData;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Dialog.fullscreen(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // 背景可点击区域
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
            // 图片轮播
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.images.length,
              itemBuilder: (context, index) {
                final imageUrl = _getImageUrl(widget.images[index]);
                if (imageUrl == null || imageUrl.isEmpty) {
                  return const Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 64,
                      color: Colors.white54,
                    ),
                  );
                }

                return Center(
                  child: InteractiveViewer(
                    panEnabled: true,
                    scaleEnabled: true,
                    minScale: 0.5,
                    maxScale: 3.0,
                    child: CachedImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                    ),
                  ),
                );
              },
            ),
            // 页面指示器
            if (widget.images.length > 1)
              Positioned(
                bottom: 100,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_currentIndex + 1} / ${widget.images.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            // 关闭按钮
            Positioned(
              top: 50,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 内联视频播放控制器
class InlineVideoControls extends StatefulWidget {
  final VideoPlayerController controller;
  final bool isBuffering;
  final VoidCallback onFullscreen;

  const InlineVideoControls({
    super.key,
    required this.controller,
    required this.isBuffering,
    required this.onFullscreen,
  });

  @override
  State<InlineVideoControls> createState() => _InlineVideoControlsState();
}

class _InlineVideoControlsState extends State<InlineVideoControls> {
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _hideControlsAfterDelay();
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _hideControlsAfterDelay();
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleControls,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // 缓冲指示器
            if (widget.isBuffering)
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 3,
                    ),
                    SizedBox(height: 8),
                    Text(
                      '缓冲中...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            
            // 控制器
            AnimatedOpacity(
              opacity: _showControls ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    // 顶部控制栏
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                          onPressed: widget.onFullscreen,
                          icon: const Icon(
                            Icons.fullscreen,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ],
                    ),
                    
                    const Spacer(),
                    
                    // 播放控制
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              if (widget.controller.value.isPlaying) {
                                widget.controller.pause();
                              } else {
                                widget.controller.play();
                              }
                            });
                          },
                          icon: Icon(
                            widget.controller.value.isPlaying
                                ? Icons.pause
                                : Icons.play_arrow,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ],
                    ),
                    
                    // 进度条和时间显示
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Text(
                            _formatDuration(widget.controller.value.position),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: VideoProgressIndicator(
                              widget.controller,
                              allowScrubbing: true,
                              colors: const VideoProgressColors(
                                playedColor: Colors.white,
                                bufferedColor: Colors.grey,
                                backgroundColor: Colors.black54,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _formatDuration(widget.controller.value.duration),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 视频播放器页面
class VideoPlayerPage extends StatefulWidget {
  final String videoUrl;

  const VideoPlayerPage({
    super.key,
    required this.videoUrl,
  });

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isBuffering = false;
  bool _isLandscape = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
      
      // 监听缓冲状态
      _controller.addListener(_videoListener);
      
      await _controller.initialize();
      
      if (mounted) {
        // 检测视频方向
        final aspectRatio = _controller.value.aspectRatio;
        _isLandscape = aspectRatio > 1.0;
        
        
        // 根据视频方向设置屏幕方向
        if (_isLandscape) {
          SystemChrome.setPreferredOrientations([
            DeviceOrientation.landscapeLeft,
            DeviceOrientation.landscapeRight,
          ]);
        } else {
          SystemChrome.setPreferredOrientations([
            DeviceOrientation.portraitUp,
            DeviceOrientation.portraitDown,
          ]);
        }
        
        setState(() {
          _isInitialized = true;
        });
        
        // 自动播放
        _controller.play();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = '视频加载失败: $e';
        });
      }
    }
  }

  void _videoListener() {
    if (!mounted) return;
    
    final bool isBuffering = _controller.value.isBuffering;
    if (_isBuffering != isBuffering) {
      setState(() {
        _isBuffering = isBuffering;
      });
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_videoListener);
    _controller.dispose();
    
    // 恢复屏幕方向为竖屏
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: _hasError
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.white,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _hasError = false;
                          _isInitialized = false;
                        });
                        _initializePlayer();
                      },
                      child: const Text('重试'),
                    ),
                  ],
                ),
              )
            : _isInitialized
                ? Stack(
                    children: [
                      // 视频播放器 - 保持比例
                      _isLandscape
                          ? Center(
                              child: AspectRatio(
                                aspectRatio: _controller.value.aspectRatio,
                                child: VideoPlayer(_controller),
                              ),
                            )
                          : Center(
                              child: AspectRatio(
                                aspectRatio: _controller.value.aspectRatio,
                                child: VideoPlayer(_controller),
                              ),
                            ),
                      // 视频控制器
                      VideoPlayerControls(
                        controller: _controller,
                        isBuffering: _isBuffering,
                      ),
                      // 返回按钮 - 叠加在视频上
                      Positioned(
                        top: 16,
                        left: 16,
                        child: GestureDetector(
                          onTap: () => Navigator.of(context).pop(),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: const BoxDecoration(
                              color: Colors.black54,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 3,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          '正在加载视频...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '请稍候',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
      ),
    );
  }
}

/// 视频播放控制器
class VideoPlayerControls extends StatefulWidget {
  final VideoPlayerController controller;
  final bool isBuffering;

  const VideoPlayerControls({
    super.key,
    required this.controller,
    required this.isBuffering,
  });

  @override
  State<VideoPlayerControls> createState() => _VideoPlayerControlsState();
}

class _VideoPlayerControlsState extends State<VideoPlayerControls> {
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _hideControlsAfterDelay();
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _hideControlsAfterDelay();
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleControls,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // 缓冲指示器
            if (widget.isBuffering)
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 3,
                    ),
                    SizedBox(height: 8),
                    Text(
                      '缓冲中...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            
            // 控制器
            AnimatedOpacity(
              opacity: _showControls ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    const Spacer(),
                    // 播放控制
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              if (widget.controller.value.isPlaying) {
                                widget.controller.pause();
                              } else {
                                widget.controller.play();
                              }
                            });
                          },
                          icon: Icon(
                            widget.controller.value.isPlaying
                                ? Icons.pause
                                : Icons.play_arrow,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ],
                    ),
                    // 进度条和时间显示
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Text(
                            _formatDuration(widget.controller.value.position),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: VideoProgressIndicator(
                              widget.controller,
                              allowScrubbing: true,
                              colors: const VideoProgressColors(
                                playedColor: Colors.white,
                                bufferedColor: Colors.grey,
                                backgroundColor: Colors.black54,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _formatDuration(widget.controller.value.duration),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}