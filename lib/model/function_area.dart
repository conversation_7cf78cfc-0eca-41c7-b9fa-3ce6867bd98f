class FunctionArea {
  final String areaModule;
  final String areaName;
  final String iconUrl;
  final String areaOpenWay;
  final String areaEntrance;
  final String badgeUrl;

  FunctionArea({
    required this.areaModule,
    required this.areaName,
    required this.iconUrl,
    required this.areaOpenWay,
    required this.areaEntrance,
    required this.badgeUrl,
  });

  factory FunctionArea.fromJson(Map<String, dynamic> json) {
    return FunctionArea(
      areaModule: json['area_module'] ?? '',
      areaName: json['area_name'] ?? '',
      iconUrl: json['icon_url'] ?? '',
      areaOpenWay: json['area_open_way'] ?? '',
      areaEntrance: json['area_entrance'] ?? '',
      badgeUrl: json['badge_url'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'area_module': areaModule,
      'area_name': areaName,
      'icon_url': iconUrl,
      'area_open_way': areaOpenWay,
      'area_entrance': areaEntrance,
      'badge_url': badgeUrl,
    };
  }
}

class FunctionAreaListResponse {
  final int code;
  final String message;
  final List<FunctionArea> data;

  FunctionAreaListResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory FunctionAreaListResponse.fromJson(Map<String, dynamic> json) {
    return FunctionAreaListResponse(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => FunctionArea.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}